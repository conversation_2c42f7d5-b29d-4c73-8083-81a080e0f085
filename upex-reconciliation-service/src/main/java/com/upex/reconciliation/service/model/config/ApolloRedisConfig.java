package com.upex.reconciliation.service.model.config;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * apollo redis配置
 */
@Data
public class ApolloRedisConfig {
    /**
     * Redis实例的连接地址
     */
    private String host;
    /**
     * Redis实例的端口号
     */
    private int port;
    /**
     * Redis实例的密码
     */
    private String password;
    /**
     * 默认数据库
     */
    private Integer database = 0;
    /**
     * 命令等待超时，单位：毫秒
     */
    private int timeout = 10000;
    /**
     * 连接池中最小空闲数
     */
    private int minIdle;
    /**
     * 最大连接数
     */
    private int maxIdle;
    /**
     * 连接池最大等待阻塞时间
     */
    private long maxWait;
    /**
     * 连接池可以分配的最大连接数。使用负值表示无限制。
     */
    private int maxActive;
    /**
     * 连接超时，单位：毫秒
     */
    private int connectTimeout = 10000;
    /**
     * 连接空闲超时，单位：毫秒
     */
    private int idleConnectionTimeout = 10000;
    /**
     * 命令失败重试次数
     */
    private int retryAttempts = 3;
    /**
     * 命令重试发送时间间隔，单位：毫秒
     */
    private int retryInterval = 1500;
    /**
     * 端点
     */
    private String endpoint;
    /**
     * 地区
     */
    private String region;
    /**
     * 密钥名称
     */
    private String secretName;

    public void setHost(String host) {
        this.host = host;
        String debugRedisHost = System.getProperty("debugRedisHost");
        if (StringUtils.isNotEmpty(debugRedisHost)) {
            this.host = debugRedisHost;
        }
    }
}

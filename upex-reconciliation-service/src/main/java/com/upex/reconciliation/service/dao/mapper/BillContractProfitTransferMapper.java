package com.upex.reconciliation.service.dao.mapper;

import com.upex.reconciliation.service.dao.entity.BillContractProfitTransfer;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【bill_contract_profit_transfer(盈亏动账记录表)】的数据库操作Mapper
 * @createDate 2023-06-09 17:19:14
 * @Entity com.upex.bill.domain.BillContractProfitTransfer
 */
@Repository("billContractProfitTransferMapper")
public interface BillContractProfitTransferMapper {

    /**
     * 根据条件 查询数据
     *
     * @param timeStart    对账开始时间
     * @param timeEnd      对账结束时间
     * @param accountType
     * @param sourceType   数据来源类型
     * @param accountParam
     * @return
     */
    List<BillContractProfitTransfer> selectByCheckOkTime(@Param("timeStart") Date timeStart,
                                                         @Param("timeEnd") Date timeEnd,
                                                         @Param("accountType") Byte accountType,
                                                         @Param("sourceType") int sourceType,
                                                         @Param("accountParam") String accountParam,
                                                         @Param("tableSuffix") String tableSuffix);

    /**
     * 查询指定对账周期的所有数据
     *
     * @param checkOkTime
     * @return
     */
    List<BillContractProfitTransfer> selectAllByCheckOkTime(@Param("checkOkTime") Date checkOkTime, @Param("pageSize") int pageSize,
                                                            @Param("tableSuffix") String tableSuffix);

    /**
     * 查询指定id列表数据
     *
     * @param idList
     * @param tableSuffix
     * @return
     */
    List<BillContractProfitTransfer> selectAllByIdList(@Param("idList") List<Long> idList,
                                                       @Param("tableSuffix") String tableSuffix);


    /**
     * 根据id 更新指定字段
     *
     * @param statusNew
     * @param statusOld
     * @param version
     * @param updateTime
     * @param id
     * @return
     */

    int updateStatus(@Param("statusNew") Integer statusNew,
                     @Param("statusOld") Integer statusOld,
                     @Param("versionNew") Long versionNew,
                     @Param("version") Long version,
                     @Param("updateTime") Date updateTime,
                     @Param("id") Long id,
                     @Param("tableSuffix") String tableSuffix);

    int updateStatusWithTransferTime(@Param("statusNew") Integer statusNew,
                                     @Param("statusOld") Integer statusOld,
                                     @Param("versionNew") Long versionNew,
                                     @Param("version") Long version,
                                     @Param("updateTime") Date updateTime,
                                     @Param("transferTime") Date transferTime,
                                     @Param("id") Long id,
                                     @Param("tableSuffix") String tableSuffix);

    /**
     * 批量插入
     *
     * @param list
     * @param tableSuffix
     */
    int batchInsert(@Param("list") List<BillContractProfitTransfer> list,
                    @Param("tableSuffix") String tableSuffix);

    /**
     * 通过状态和时间查询延迟动账资产
     *
     * @param checkOkTime 检查时间
     * @return {@link List< BillContractProfitTransfer> }
     * <AUTHOR>
     * @date 2023/6/13 18:36
     */
    List<BillContractProfitTransfer> selectDelayedTransactionByStatusAndTime(@Param("checkOkTime") Date checkOkTime,
                                                                             @Param("tableSuffix") String tableSuffix);


    /**
     * 通过状态和时间查询延迟动账资产
     *
     * @param checkOkTime 检查时间
     * @return {@link List< BillContractProfitTransfer> }
     * <AUTHOR>
     * @date 2023/6/13 18:36
     */
    List<BillContractProfitTransfer> selectEachDelayedTransactionByStatusAndTime(@Param("startTime") Date startTime,
                                                                                 @Param("checkOkTime") Date checkOkTime,
                                                                                 @Param("toAccountType") Byte toAccountType,
                                                                                 @Param("tableSuffix") String tableSuffix);

    /**
     * 通过时间查询延迟动账资产
     *
     * @param checkOkTime 检查时间
     * @param tableSuffix
     * @return {@link List< BillContractProfitTransfer> }
     * <AUTHOR>
     * @date 2023/6/13 18:36
     */
    List<BillContractProfitTransfer> selectDelayedTransactionByTime(@Param("checkOkTime") Date checkOkTime,
                                                                    @Param("tableSuffix") String tableSuffix);

    /**
     * 通过转入动账类型和时间查询延迟动账资产
     *
     * @param checkOkTime   检查时间
     * @param transferTypes
     * @param tableSuffix
     * @return {@link List< BillContractProfitTransfer> }
     * <AUTHOR>
     * @date 2023/6/13 18:36
     */
    List<BillContractProfitTransfer> selectListByToAccountTypeStatusAndTime(@Param("checkOkTime") Date checkOkTime,
                                                                            @Param("toAccountType") Integer toAccountType,
                                                                            @Param("transferTypes") List<Integer> transferTypes,
                                                                            @Param("tableSuffix") String tableSuffix);

    /**
     * 通过转入动账类型和时间查询延迟动账资产
     *
     * @param checkOkTime   检查时间
     * @param transferTypes
     * @param tableSuffix
     * @return {@link List< BillContractProfitTransfer> }
     * <AUTHOR>
     * @date 2023/6/13 18:36
     */
    List<BillContractProfitTransfer> selectListByToAccountTypeTime(@Param("checkOkTime") Date checkOkTime,
                                                                   @Param("toAccountType") Integer toAccountType,
                                                                   @Param("transferTypes") List<Integer> transferTypes,
                                                                   @Param("tableSuffix") String tableSuffix);

    /**
     * 根据动账表id、状态、数据来源查询动账数据
     *
     * @param idList
     * @param tableSuffix
     * @return
     */
    List<BillContractProfitTransfer> selectTransferByIds(@Param("idList") List<Long> idList, @Param("status") Integer status, @Param("sourceType") Integer sourceType, String tableSuffix);

    List<BillContractProfitTransfer> selectDelayedTransactionByTimeAndType(@Param("checkOkTime") Date checkOkTime,
                                                                           @Param("transferTypes") List<Integer> transferTypes,
                                                                           @Param("tableSuffix") String tableSuffix);

    List<BillContractProfitTransfer> selectDelayedTransactionByStatusAndTimeAndType(@Param("checkOkTime") Date checkOkTime,
                                                                                    @Param("transferTypes") List<Integer> transferTypes,
                                                                                    @Param("tableSuffix") String tableSuffix);

    /**
     * 更具状态和时间查询动账记录
     *
     * @param status
     * @param tableSuffix
     * @return {@link List< BillContractProfitTransfer> }
     * <AUTHOR>
     * @date 2023/12/4 11:54
     */
    List<BillContractProfitTransfer> selectTransferRecordsByStatusAndTime(@Param("status") Integer status,
                                                                          @Param("tableSuffix") String tableSuffix);

    /**
     * 批量删除数据
     *
     * @param accountType
     * @param accountParam
     * @param checkTime
     * @return
     */
    int deleteByCheckTime(@Param("accountType") Byte accountType,
                          @Param("accountParam") String accountParam,
                          @Param("checkTime") Date checkTime,
                          @Param("tableSuffix") String tableSuffix);

    /**
     * 批量删除
     *
     * @param beginId
     * @param pageSize
     * @param accountType
     * @param accountParam
     * @param tableSuffix
     * @return
     */
    Boolean batchDelete(@Param("beginId") Long beginId,
                        @Param("pageSize") Long pageSize,
                        @Param("accountType") Byte accountType,
                        @Param("accountParam") String accountParam,
                        @Param("tableSuffix") String tableSuffix);

    List<BillContractProfitTransfer> selectListByAccountTypeAndCheckTime(@Param("accountType") Byte accountType,
                                                                         @Param("accountParam") String accountParam,
                                                                         @Param("checkTime") Date checkTime,
                                                                         @Param("tableSuffix") String tableSuffix);

    /**
     * 创建表
     *
     * @param tableSuffix
     * @return
     */
    Integer createTable(@Param("tableSuffix") String tableSuffix);

    /**
     * 根据时间获取所有业务线待动账金额
     *
     * @param checkOkTime
     * @param transferTypes
     * @param tableSuffix
     * @return
     */
    List<BillContractProfitTransfer> selectAllUnProfitTransferByCheckOkTime(
            @Param("accountTypeList") List<Byte> accountTypeList,
            @Param("checkOkTime") Date checkOkTime,
            @Param("transferTypes") List<Integer> transferTypes,
            @Param("tableSuffix") String tableSuffix);

    /**
     * 根据时间获取所有业务线待动账金额
     *
     * @param checkOkTime
     * @param transferTypes
     * @param tableSuffix
     * @return
     */
    List<BillContractProfitTransfer> getUserUnProfitTransfer(
            @Param("userId") Long userId,
            @Param("toAccountTypes") List<Byte> toAccountTypes,
            @Param("checkOkTime") Date checkOkTime,
            @Param("transferTime") Date transferTime,
            @Param("transferTypes") List<Integer> transferTypes,
            @Param("tableSuffix") String tableSuffix);

    /**
     * 获取未动账最小 checkOkTime
     *
     * @param tableSuffix
     * @return
     */
    Date getNotProfitTransferMinCheckOkTime(
            @Param("tableSuffix") String tableSuffix);

    /**
     * 查询指定对账周期的所有数据
     *
     * @param checkOkTime
     * @return
     */
    List<BillContractProfitTransfer> selectAllByToAccountTypeAndCheckOkTime(
            @Param("checkOkTime") Date checkOkTime,
            @Param("pageSize") int pageSize,
            @Param("toAccountTypeList") List<Byte> toAccountTypeList,
            @Param("tableSuffix") String tableSuffix);
}

package com.upex.reconciliation.service.dao.mapper;

import com.upex.reconciliation.service.dao.entity.BillContractProfitSymbolDetail;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【bill_contract_profit_symbol_detail(币种+币对维度盈亏明细表)】的数据库操作Mapper
 * @createDate 2023-06-09 17:19:14
 * @Entity com.upex.bill.domain.BillContractProfitSymbolDetail
 */
@Repository("billContractProfitSymbolDetailMapper")
public interface BillContractProfitSymbolDetailMapper {

    int batchInsert(@Param("accountType") Byte accountType,
                    @Param("accountParam") String accountParam,
                    @Param("records") List<BillContractProfitSymbolDetail> records);

    int batchInsertHis(@Param("accountType") Byte accountType,
                       @Param("accountParam") String accountParam,
                       @Param("records") List<BillContractProfitSymbolDetail> records);

    List<BillContractProfitSymbolDetail> getBillContractProfitSymbols(@Param("accountType") Byte accountType,
                                                                      @Param("accountParam") String accountParam, @Param("checkTime") Date checkTime);

    List<BillContractProfitSymbolDetail> getAllBillContractProfitSymbols(@Param("accountType") Byte accountType,
                                                                         @Param("accountParam") String accountParam,
                                                                         @Param("startTime") Date startTime,
                                                                         @Param("endTime") Date endTime);

    /**
     * 删除重置对账时间之后的记录
     *
     * @param resetCheckTime
     * @param accountType
     * @param accountParam
     * @return
     */
    int deleteAfterRecord(@Param("resetCheckTime") Date resetCheckTime,
                          @Param("accountType") Byte accountType,
                          @Param("accountParam") String accountParam);

    List<BillContractProfitSymbolDetail> getAllAfterRecord(@Param("resetCheckTime") Date resetCheckTime,
                                                           @Param("accountType") Byte accountType,
                                                           @Param("accountParam") String accountParam);

    /**
     * 查询最新对账检查时间
     *
     * @param accountType
     * @param accountParam
     * @param profitType
     * @return
     */
    Date getLastCheckOkTime(@Param("accountType") Byte accountType,
                            @Param("accountParam") String accountParam,
                            @Param("profitType") String profitType);

    /**
     * 批量删除
     *
     * @param accountType
     * @param accountParam
     * @param checkTime
     * @return
     */
    int deleteByCheckTime(@Param("accountType") Byte accountType,
                          @Param("accountParam") String accountParam,
                          @Param("checkTime") Date checkTime);

    /**
     * 批量删除
     *
     * @param beginId
     * @param pageSize
     * @param accountType
     * @param accountParam
     * @return
     */
    Boolean batchDelete(@Param("beginId") Long beginId,
                        @Param("pageSize") Long pageSize,
                        @Param("accountType") Byte accountType,
                        @Param("accountParam") String accountParam);

    List<BillContractProfitSymbolDetail> selectListByAccountTypeAndCheckTime(@Param("accountType") Byte accountType,
                                                                             @Param("accountParam") String accountParam,
                                                                             @Param("checkTime") Date checkTime);

    /**
     * 查询数据对比
     *
     * @param accountType
     * @param accountParam
     * @param profitTypeList
     * @param startTime
     * @param endTime
     * @return
     */
    List<BillContractProfitSymbolDetail> getAllContractProfitSymbolDetailList(@Param("accountType") Byte accountType,
                                                                              @Param("accountParam") String accountParam,
                                                                              @Param("profitTypeList") List<String> profitTypeList,
                                                                              @Param("checkOkTime") Date checkOkTime);

    /**
     * 单条更新
     *
     * @param accountType
     * @param accountParam
     * @param record
     * @return
     */
    int updateById(@Param("accountType") Byte accountType,
                   @Param("accountParam") String accountParam,
                   @Param("record") BillContractProfitSymbolDetail record);
}

package com.upex.reconciliation.service.dao.mapper;

import com.upex.reconciliation.service.dao.entity.BillContractProfitCoinDetail;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【bill_contract_profit_coin_detail(币种维度盈亏明细表)】的数据库操作Mapper
 * @createDate 2023-06-09 17:19:14
 * @Entity com.upex.bill.domain.BillContractProfitCoinDetail
 */
@Repository("billContractProfitCoinDetailMapper")
public interface BillContractProfitCoinDetailMapper {
    List<BillContractProfitCoinDetail> getBillContractProfitCoinDetailList(@Param("accountType") Byte accountType,
                                                                           @Param("accountParam") String accountParam,
                                                                           @Param("checkOkTime") Date checkOkTime,
                                                                           @Param("profitType") String profitType);

    /**
     * 查询范围内的数据
     *
     * @param accountType
     * @param accountParam
     * @param profitTypeList
     * @param startTime
     * @param endTime
     * @return
     */
    List<BillContractProfitCoinDetail> getAllBillContractProfitCoinDetailList(@Param("accountType") Byte accountType,
                                                                              @Param("accountParam") String accountParam,
                                                                              @Param("profitTypeList") List<String> profitTypeList,
                                                                              @Param("checkOkTime") Date checkOkTime);

    int batchInsert(@Param("accountType") Byte accountType,
                    @Param("accountParam") String accountParam,
                    @Param("list") List<BillContractProfitCoinDetail> list);

    int batchInsertHis(@Param("accountType") Byte accountType,
                       @Param("accountParam") String accountParam,
                       @Param("list") List<BillContractProfitCoinDetail> list);

    /**
     * 更新数据状态 和更新时间
     *
     * @param newStatus  目标状态
     * @param oldStatus  历史状态
     * @param updateTime 更新时间
     * @param ids        要更新的id
     * @return
     */
    int batchUpdateStatus(@Param("accountType") Byte accountType,
                          @Param("accountParam") String accountParam,
                          @Param("newStatus") int newStatus,
                          @Param("oldStatus") int oldStatus,
                          @Param("updateTime") Date updateTime,
                          @Param("ids") List<Long> ids);

    /**
     * 删除重置对账时间之后的记录
     *
     * @param resetCheckTime
     * @param accountType
     * @param accountParam
     * @return
     */
    int deleteAfterRecord(@Param("resetCheckTime") Date resetCheckTime,
                          @Param("accountType") Byte accountType,
                          @Param("accountParam") String accountParam);

    /**
     * 删除重置对账时间之后的记录
     *
     * @param resetCheckTime
     * @param accountType
     * @param accountParam
     * @return
     */
    List<BillContractProfitCoinDetail> getAllAfterRecord(@Param("resetCheckTime") Date resetCheckTime,
                                                         @Param("accountType") Byte accountType,
                                                         @Param("accountParam") String accountParam);

    /**
     * 查询最新的对账时间数据
     *
     * @param accountType
     * @param accountParam
     * @param profitType
     * @return
     */
    Date getLastCheckOkTime(Byte accountType, String accountParam, String profitType);

    /**
     * 单条更新
     *
     * @param accountType
     * @param accountParam
     * @param record
     * @return
     */
    int updateById(@Param("accountType") Byte accountType,
                   @Param("accountParam") String accountParam,
                   @Param("record") BillContractProfitCoinDetail record);

    /**
     * 批量删除数据
     *
     * @param accountType
     * @param accountParam
     * @param checkTime
     * @return
     */
    int deleteByCheckTime(@Param("accountType") Byte accountType,
                          @Param("accountParam") String accountParam,
                          @Param("checkTime") Date checkTime);

    /**
     * 批量删除
     *
     * @param beginId
     * @param pageSize
     * @param accountType
     * @param accountParam
     * @return
     */
    Boolean batchDelete(@Param("beginId") Long beginId,
                        @Param("pageSize") Long pageSize,
                        @Param("accountType") Byte accountType,
                        @Param("accountParam") String accountParam);

    List<BillContractProfitCoinDetail> selectListByAccountTypeAndCheckTime(@Param("accountType") Byte accountType,
                                                                           @Param("accountParam") String accountParam,
                                                                           @Param("checkTime") Date checkTime);
}

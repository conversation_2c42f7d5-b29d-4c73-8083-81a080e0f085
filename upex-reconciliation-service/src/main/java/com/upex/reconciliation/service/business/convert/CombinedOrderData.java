package com.upex.reconciliation.service.business.convert;

import com.upex.spot.dto.enums.SpotBillBizTypeEnum;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
public class CombinedOrderData {

    // 主订单数据
    private ConvertOrder mainOrder;

    // 流水订单数据，按业务类型分类
    private Map<String, ConvertBill> flowOrders = new HashMap<>();

    private List<ConvertBill> flowOrderList = new ArrayList<>();

    /**
     * 添加流水订单
     *
     * @param bizType   业务类型
     * @param flowOrder 流水订单数据
     */
    public void addFlowOrder(String bizType, ConvertBill flowOrder) {
        flowOrders.put(bizType, flowOrder);
    }

    /**
     * 获取流水订单
     *
     * @param bizType 业务类型
     * @return 流水订单数据
     */
    public ConvertBill getFlowOrder(String bizType) {
        return flowOrders.get(bizType);
    }

    /**
     * 检查是否包含指定业务类型的流水订单
     *
     * @param bizType 业务类型
     * @return 是否包含
     */
    public boolean containsFlowOrder(String bizType) {
        return flowOrders.containsKey(bizType);
    }

    /**
     * 获取订单ID
     *
     * @return 订单ID
     */
    public Long getOrderId() {
        return mainOrder != null ? mainOrder.getOrderId() : null;
    }

//    public boolean isCoinTypeConsistent() {
//        // 判断币种是否一致
//        return mainOrder.getFromCoinId().equals(flowOrders.get(SpotBillBizTypeEnum.CONVERT_USER_OUT.getCode().toString()).getCoinId()) &&
//                mainOrder.getFromCoinId().equals(flowOrders.get(SpotBillBizTypeEnum.CONVERT_EXCHANGE_SYSTEM_IN.getCode().toString()).getCoinId()) &&
//                mainOrder.getToCoinId().equals(flowOrders.get(SpotBillBizTypeEnum.CONVERT_EXCHANGE_SYSTEM_OUT.getCode().toString()).getCoinId()) &&
//                mainOrder.getToCoinId().equals(flowOrders.get(SpotBillBizTypeEnum.CONVERT_USER_IN.getCode().toString()).getCoinId());
//    }




}

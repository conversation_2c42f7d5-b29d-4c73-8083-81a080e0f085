package com.upex.reconciliation.service.business.convert.task;

import com.upex.convert.facade.params.OrderVO;
import com.upex.reconciliation.service.business.convert.CombinedOrderData;
import com.upex.reconciliation.service.business.convert.ConvertBill;
import com.upex.reconciliation.service.business.convert.OrderDataMatcher;
import com.upex.reconciliation.service.business.convert.enums.ReconOrderStausEnum;
import com.upex.reconciliation.service.business.convert.enums.ReconOrderTypeEnum;
import com.upex.reconciliation.service.business.convert.remote.ConvertService;
import com.upex.reconciliation.service.business.convert.remote.SpotBillService;
import com.upex.reconciliation.service.dao.entity.ReconOrderFailureRecord;
import com.upex.reconciliation.service.model.config.ReconOrderConfig;
import com.upex.reconciliation.service.service.ReconOrderFailureRecordService;
import com.upex.reconciliation.service.utils.ReconciliationApolloConfigUtils;
import com.upex.spot.dto.result.bill.NewSpotBillInfoResult;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.*;
import static com.upex.reconciliation.service.business.convert.ReconOrderConstants.*;


@Component
@Slf4j
public class DelayReconOrderTask implements Runnable {

    @Resource
    private ReconOrderFailureRecordService reconOrderFailureRecordService;

    @Resource(name = "redisTemplate")
    private RedisTemplate<String, Object> redisTemplate;

    @Resource
    private OrderDataMatcher orderDataMatcher;

    @Resource
    private ConvertService convertService;

    @Resource
    private SpotBillService spotBillService;

    private final DelayQueue<DelayedReconRecord> delayQueue = new DelayQueue<>();
    private final ExecutorService executor = Executors.newSingleThreadExecutor(r -> new Thread(r, "DelayOrderReconThread"));


    @PostConstruct
    public void init() {
        // loadFailedRecordsOnStartup();
        executor.submit(this);
    }

    @PreDestroy
    public void destroy() {
        executor.shutdownNow();
    }

    /**
     * 启动时加载未处理的超时失败记录到延迟队列
     */
    private void loadFailedRecordsOnStartup() {
        log.info("Loading unprocessed failure records into delay queue...");
        // 加载状态为待处理(0)，失败类型为超时(2)的记录
        List<ReconOrderFailureRecord> failedRecords = reconOrderFailureRecordService.getUnprocessedRecords(ReconOrderTypeEnum.CONVERT.getCode(), ReconOrderStausEnum.NOT_RECONCILED.getCode()); // bizType=1 for convert
        if (failedRecords != null) {
            for (ReconOrderFailureRecord record : failedRecords) {
                addRecordToQueue(record, 10);
            }
            log.info("Loaded {} unprocessed failure records.", failedRecords.size());
        }
    }

    /**
     * 将失败记录添加到延迟队列进行处理
     * @param record 失败记录
     * @param delayInSeconds 延迟处理的秒数
     */
    public void addRecordToQueue(ReconOrderFailureRecord record, long delayInSeconds) {
        if (record == null) return;
        long triggerTime = System.currentTimeMillis() + (delayInSeconds * 1000);
        delayQueue.offer(new DelayedReconRecord(record, triggerTime));
        log.info("Added order {} to delay queue for reprocessing in {} seconds.", record.getOrderId(), delayInSeconds);
    }

    @Override
    public void run() {
        log.info("DelayOrderReconTask started.");
        while (!Thread.currentThread().isInterrupted()) {
            try {
                DelayedReconRecord delayedRecord = delayQueue.take();
                processRecord(delayedRecord.getRecord());
            } catch (InterruptedException e) {
                log.warn("DelayOrderReconTask interrupted.");
                Thread.currentThread().interrupt();
            } catch (Exception e) {
                log.error("Error processing delay queue", e);
            }
        }
    }

    private void processRecord(ReconOrderFailureRecord record) {
        ReconOrderConfig reconOrderConfig = ReconciliationApolloConfigUtils.getReconOrderConfig(ReconOrderTypeEnum.CONVERT);
        Long orderId = record.getOrderId();
        Long accountId = record.getAccountId();
        Long systemAccountId = reconOrderConfig.getSystemUserId();
        // 获取主订单
        OrderVO convertOrder = convertService.getConvertOrder(orderId, record.getAccountId());
        if (convertOrder == null) {
            handleMissingData(record);
            return;
        }
        // 获取用户流水订单
        List<NewSpotBillInfoResult> spotBill = spotBillService.getSpotBill(accountId, orderId);
        if (spotBill == null) {
            handleMissingData(record);
            return;
        }
        // 获取系统流水
        List<NewSpotBillInfoResult> systemBill = spotBillService.getSpotBill(systemAccountId, orderId);
        if (systemBill == null) {
            handleMissingData(record);
            return;
        }
        // 封装为CombinedOrderData

        CombinedOrderData combinedOrderData = new CombinedOrderData();
        combinedOrderData.setMainOrder();


        handleReconciliation(record);


    }

    private void handleMissingData(ReconOrderFailureRecord record) {
        // todo
        String orderId = String.valueOf(record.getOrderId());
        log.warn("Data missing for order {}. Retrying...", orderId);

        int retryCount = (record.getRetryCount() == null ? 0 : record.getRetryCount()) + 1;
        // 更新数据库中的重试次数
        reconOrderFailureRecordService.updateRetryCount(record.getId(), retryCount);
        record.setRetryCount(retryCount); // 更新内存中的对象

        ReconOrderConfig reconOrderConfig = ReconciliationApolloConfigUtils.getReconOrderConfig(ReconOrderTypeEnum.CONVERT);
        if (retryCount >= reconOrderConfig.getRetryCount()) {
            log.error("ALERT: Order {} exceeded retry threshold ({}). Setting status to failed.", orderId, reconOrderConfig.getRetryCount());
            reconOrderFailureRecordService.updateStatus(record.getId(), (byte) 2, "system", "超过重试阈值");
        } else {
            // 使用指数退避策略重新入队 (1, 2, 4, 8... 分钟)
            long delay = (long) (60 * Math.pow(2, retryCount - 1));
            log.info("Re-queuing order {} for retry {} in {} seconds.", orderId, retryCount, delay);
            addRecordToQueue(record, delay);
        }
    }

    private void handleReconciliation(ReconOrderFailureRecord record) {
        String orderId = String.valueOf(record.getOrderId());
        log.info("All data present for order {}. Attempting final reconciliation.", orderId);
        orderDataMatcher.tryMatchOrder(orderId);

        // 通过检查主订单key是否已被删除来判断对账是否成功
        if (Boolean.FALSE.equals(redisTemplate.hasKey(MAIN_ORDER_PREFIX + orderId))) {
            log.info("Reconciliation successful for order {}.", orderId);
            reconOrderFailureRecordService.updateStatus(record.getId(), (byte) 1, "system", "延迟对账成功");
        } else {
            log.error("ALERT: Reconciliation failed for order {} even with all data present. This is not retryable. Setting status to failed.", orderId);
            reconOrderFailureRecordService.updateStatus(record.getId(), (byte) 2, "system", "对账不通过");
        }
    }

    /**
     * 用于DelayQueue的内部包装类
     */
    @Getter
    private static class DelayedReconRecord implements Delayed {
        private final ReconOrderFailureRecord record;
        private final long triggerTime;

        public DelayedReconRecord(ReconOrderFailureRecord record, long triggerTime) {
            this.record = record;
            this.triggerTime = triggerTime;
        }

        @Override
        public long getDelay(TimeUnit unit) {
            long diff = triggerTime - System.currentTimeMillis();
            return unit.convert(diff, TimeUnit.MILLISECONDS);
        }

        @Override
        public int compareTo(Delayed o) {
            return Long.compare(this.triggerTime, ((DelayedReconRecord) o).triggerTime);
        }
    }
}
